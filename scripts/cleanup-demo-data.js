/**
 * Demo Data Cleanup Script
 * 
 * This script removes all demo/seed data from the database to prepare
 * for real data collection. It will:
 * 1. Remove demo blog posts
 * 2. Remove demo categories (keeping only real ones)
 * 3. Remove demo catalogue items (keeping only real ones)
 * 4. Remove demo transactions
 * 5. Clear any tracking data
 * 
 * Run with: node scripts/cleanup-demo-data.js
 * Use --force flag to skip confirmation: node scripts/cleanup-demo-data.js --force
 */

const { PrismaClient } = require('@prisma/client');
const readline = require('readline');

const prisma = new PrismaClient();

// Demo blog post titles to identify and remove
const DEMO_BLOG_TITLES = [
  'The Importance of Responsive Design in 2023',
  'Importance of Responsive Design in 2023',
  '5 Logo Design Trends to Watch in 2023',
  'Logo Design Trends to Watch in 2023',
  'How to Create an Effective Social Media Strategy',
  'Effective Social Media Strategy',
  'The Future of Web Development',
  'Web Development Trends',
  'Digital Marketing Best Practices',
  'SEO Optimization Guide',
  'Brand Identity Design',
  'User Experience Design',
  'Mobile App Development',
  'E-commerce Solutions',
  'Content Marketing Strategy'
];

// Demo categories to remove
const DEMO_CATEGORIES = [
  'web-design',
  'graphic-design', 
  'digital-marketing',
  'branding',
  'development'
];

// Demo catalogue services to remove (keeping real business services)
const DEMO_CATALOGUE_SERVICES = [
  'Sample Service',
  'Demo Service',
  'Test Service'
];

async function askConfirmation(message) {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question(`${message} (y/N): `, (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

async function checkCurrentData() {
  console.log('🔍 Checking current database state...\n');
  
  const eventCount = await prisma.eventTracking.count();
  const blogCount = await prisma.blogPost.count();
  const categoryCount = await prisma.category.count();
  const catalogueCount = await prisma.catalogue.count();
  const transactionCount = await prisma.transaction.count();
  
  console.log('Current data counts:');
  console.log(`📊 Event Tracking: ${eventCount} records`);
  console.log(`📝 Blog Posts: ${blogCount} records`);
  console.log(`📂 Categories: ${categoryCount} records`);
  console.log(`🛍️ Catalogue Items: ${catalogueCount} records`);
  console.log(`💳 Transactions: ${transactionCount} records`);
  
  return { eventCount, blogCount, categoryCount, catalogueCount, transactionCount };
}

async function identifyDemoData() {
  console.log('\n🔍 Identifying demo data...\n');
  
  // Find demo blog posts
  const demoBlogPosts = await prisma.blogPost.findMany({
    where: {
      OR: DEMO_BLOG_TITLES.map(title => ({
        title: { contains: title, mode: 'insensitive' }
      }))
    },
    select: { id: true, title: true, author: true }
  });
  
  // Find demo categories
  const demoCategories = await prisma.category.findMany({
    where: {
      slug: { in: DEMO_CATEGORIES }
    },
    select: { id: true, name: true, slug: true }
  });
  
  // Find demo catalogue items
  const demoCatalogueItems = await prisma.catalogue.findMany({
    where: {
      OR: DEMO_CATALOGUE_SERVICES.map(service => ({
        service: { contains: service, mode: 'insensitive' }
      }))
    },
    select: { id: true, service: true }
  });
  
  console.log('Demo data identified:');
  console.log(`📝 Demo Blog Posts: ${demoBlogPosts.length}`);
  if (demoBlogPosts.length > 0) {
    demoBlogPosts.forEach(post => {
      console.log(`  - "${post.title}" by ${post.author || 'Unknown'}`);
    });
  }
  
  console.log(`📂 Demo Categories: ${demoCategories.length}`);
  if (demoCategories.length > 0) {
    demoCategories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.slug})`);
    });
  }
  
  console.log(`🛍️ Demo Catalogue Items: ${demoCatalogueItems.length}`);
  if (demoCatalogueItems.length > 0) {
    demoCatalogueItems.forEach(item => {
      console.log(`  - ${item.service}`);
    });
  }
  
  return { demoBlogPosts, demoCategories, demoCatalogueItems };
}

async function cleanupDemoData(force = false) {
  try {
    console.log('🧹 Starting demo data cleanup...\n');
    
    // Check current state
    const currentData = await checkCurrentData();
    
    // Identify demo data
    const { demoBlogPosts, demoCategories, demoCatalogueItems } = await identifyDemoData();
    
    // Ask for confirmation unless force flag is used
    if (!force) {
      console.log('\n⚠️  WARNING: This will permanently delete the identified demo data.');
      console.log('Real business data (like actual transactions, real blog posts, etc.) will be preserved.');
      
      const confirmed = await askConfirmation('\nDo you want to proceed with cleanup?');
      if (!confirmed) {
        console.log('❌ Cleanup cancelled by user.');
        return;
      }
    }
    
    console.log('\n🗑️ Starting cleanup process...\n');
    
    // 1. Clear all event tracking data (this is all demo/test data)
    if (currentData.eventCount > 0) {
      console.log('🧹 Clearing event tracking data...');
      const deletedEvents = await prisma.eventTracking.deleteMany({});
      console.log(`✅ Deleted ${deletedEvents.count} event tracking records`);
    }
    
    // 2. Remove demo blog posts
    if (demoBlogPosts.length > 0) {
      console.log('🧹 Removing demo blog posts...');
      const deletedPosts = await prisma.blogPost.deleteMany({
        where: {
          id: { in: demoBlogPosts.map(post => post.id) }
        }
      });
      console.log(`✅ Deleted ${deletedPosts.count} demo blog posts`);
    }
    
    // 3. Remove demo categories
    if (demoCategories.length > 0) {
      console.log('🧹 Removing demo categories...');
      const deletedCategories = await prisma.category.deleteMany({
        where: {
          id: { in: demoCategories.map(cat => cat.id) }
        }
      });
      console.log(`✅ Deleted ${deletedCategories.count} demo categories`);
    }
    
    // 4. Remove demo catalogue items
    if (demoCatalogueItems.length > 0) {
      console.log('🧹 Removing demo catalogue items...');
      const deletedCatalogue = await prisma.catalogue.deleteMany({
        where: {
          id: { in: demoCatalogueItems.map(item => item.id) }
        }
      });
      console.log(`✅ Deleted ${deletedCatalogue.count} demo catalogue items`);
    }
    
    // 5. Note about transactions - we'll keep them as they might be real
    console.log('\n📝 Note: Keeping existing transactions as they may be real business data.');
    console.log('If you need to remove specific transactions, please do so manually through the admin panel.');
    
    console.log('\n🎉 Demo data cleanup completed successfully!');
    console.log('\n📊 Final state:');
    await checkCurrentData();
    
    console.log('\n✅ Your system is now ready to collect real data!');
    console.log('💡 The analytics system will now use real Google Analytics data instead of mock data.');
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

async function main() {
  try {
    const force = process.argv.includes('--force');
    await cleanupDemoData(force);
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { cleanupDemoData };
